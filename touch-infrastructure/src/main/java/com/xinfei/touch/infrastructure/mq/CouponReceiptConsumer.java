package com.xinfei.touch.infrastructure.mq;

import com.alibaba.fastjson.JSON;
import com.xinfei.touch.application.dto.CouponCallbackDTO;
import com.xinfei.touch.application.service.ReceiptApplicationService;
import com.xinfei.touch.infrastructure.mq.dto.CouponCallbackVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠券回执RocketMQ消费者
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = "${coupon.callback.topic:tp_xyf_cdp_coupon_callback}",
    consumerGroup = "${coupon.callback.consumer.group:cg_xyf_cdp_coupon_callback}"
)
public class CouponReceiptConsumer implements RocketMQListener<String> {
    
    private final ReceiptApplicationService receiptApplicationService;
    
    @Override
    public void onMessage(String message) {
        try {
            log.info("收到优惠券回执消息: {}", message);
            
            // 解析消息
            List<CouponCallbackVO> couponCallbackList = JSON.parseArray(message, CouponCallbackVO.class);
            if (couponCallbackList == null || couponCallbackList.isEmpty()) {
                log.warn("优惠券回执消息为空: {}", message);
                return;
            }
            
            // 转换为应用层DTO
            List<CouponCallbackDTO> couponCallbackDTOList = couponCallbackList.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 统一回执处理
            receiptApplicationService.processCouponReceipts(couponCallbackDTOList);

            log.info("优惠券回执处理完成: count={}", couponCallbackDTOList.size());
            
        } catch (Exception e) {
            log.error("优惠券回执处理异常: message={}", message, e);
            // 注意：这里不抛出异常，避免消息重复消费
        }
    }

    /**
     * 转换为应用层DTO
     */
    private CouponCallbackDTO convertToDTO(CouponCallbackVO vo) {
        CouponCallbackDTO dto = new CouponCallbackDTO();
        BeanUtils.copyProperties(vo, dto);
        return dto;
    }
}
