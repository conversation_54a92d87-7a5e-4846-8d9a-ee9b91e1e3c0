<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userDispatchFailDetailMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="appUserId" column="app_user_id" jdbcType="BIGINT"/>
        <result property="app" column="app" jdbcType="VARCHAR"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="marketChannel" column="market_channel" jdbcType="SMALLINT"/>
        <result property="templateType" column="template_type" jdbcType="SMALLINT"/>
        <result property="templateNo" column="template_no" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="dispatchTime" column="dispatch_time" jdbcType="TIMESTAMP"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="dispatchType" column="dispatch_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,app_user_id,app,strategy_id,market_channel,template_type,template_no,status,dispatch_time,fail_reason,created_time,updated_time,group_name
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_dispatch_fail_detail
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo"
            useGeneratedKeys="true">
        insert into user_dispatch_fail_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="appUserId != null">app_user_id,</if>
            <if test="app != null">app,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="templateType != null">template_type,</if>
            <if test="templateNo != null">template_no,</if>
            <if test="status != null">status,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="groupName != null">group_name,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="bizEventType != null">biz_event_type,</if>
            <if test="dispatchType != null">dispatch_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="appUserId != null">#{appUserId,jdbcType=BIGINT},</if>
            <if test="app != null">#{app,jdbcType=VARCHAR},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="templateType != null">#{templateType,jdbcType=SMALLINT},</if>
            <if test="templateNo != null">#{templateNo,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="dispatchTime != null">#{dispatchTime,jdbcType=TIMESTAMP},</if>
            <if test="failReason != null">#{failReason,jdbcType=VARCHAR},</if>
            <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="bizEventType != null">#{bizEventType,jdbcType=VARCHAR},</if>
            <if test="dispatchType != null">#{dispatchType,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo">
        update user_dispatch_fail_detail
        <set>
            <if test="appUserId != null">
                app_user_id = #{appUserId,jdbcType=BIGINT},
            </if>
            <if test="app != null">
                app = #{app,jdbcType=VARCHAR},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateType != null">
                template_type = #{templateType,jdbcType=SMALLINT},
            </if>
            <if test="templateNo != null">
                template_no = #{templateNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
