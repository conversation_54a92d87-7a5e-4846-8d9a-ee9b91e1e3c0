/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.oss;

import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.oss.dto.OssContentResponse;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.utils.AesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 根据strategyId, crowd_slice_id,start_pos,end_pos读取对应的OSS文件内容，返回crowd_detail_list
 * 读取后可知道该分片有多少待执行用户。
 * <AUTHOR>
 * @version $ OssReaderService, v 0.1 2025/4/10 17:47 xu.fan Exp $
 */
@Component
@Slf4j
public class StrategyOssReaderService {

    @Autowired
    private RangeReadFileService rangeReadFileService;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private CrowdSliceRepository crowdSliceRepository;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private Config config;

    /**
     * 读取单分片数据
     * @param strategyExecLogDo
     * @return
     */
    public List<CrowdDetailDo> readBySliceExecLog(StrategySliceExecLogDo strategyExecLogDo) throws StrategyException {

        CrowdSliceDo crowdSliceDo = crowdSliceRepository.selectByPrimaryKey(strategyExecLogDo.getCrowdSliceId());
        try {
            // 读取文件首行，确定字段顺序
            OssContentResponse ossContentResponse = rangeReadFileService.readByRange(crowdSliceDo.getOssUri(), crowdSliceDo.getStartPos(),
                    crowdSliceDo.getEndPos());
            // 转为CrowdDetailDO
            if(ossContentResponse != null && ossContentResponse.isSuccess()) {
                if(!CollectionUtils.isEmpty(ossContentResponse.getContentList())) {
                    List<CrowdDetailDo> resultList = buildCrowdDetailDo(strategyExecLogDo.getCrowdId(), ossContentResponse);
                    return resultList;
                } else {
                    Collections.emptyList();
                }
            }

        } catch (Exception e) {
            log.error("readBySliceExecLog error", e);
            throw new StrategyException("readBySliceExecLog error " + e.getMessage());
        }
        return null;
    }

    private List<CrowdDetailDo> buildCrowdDetailDo(Long crowdId, OssContentResponse ossContentResponse) throws StrategyException {
        List<String> headerName = ossContentResponse.getColumnNames();
        List<CrowdDetailDo> crowdDetailDoList = ossContentResponse.getContentList().stream().map(line -> {
            String[] columns = line.split(",");
            return headerValueMatch(crowdId, headerName, columns);
        }).collect(Collectors.toList());
        return crowdDetailDoList;
    }

    /**
     * __header__,inner_app,ab_num,app_user_id,register_time,mobile,app,app_user_id_last2,run_version,__footer__
     * 相比StarRock方案减少cno字段
     * @param headerName
     * @param columns
     * @return
     * @throws StrategyException
     */
    private CrowdDetailDo headerValueMatch(Long crowdId, List<String> headerName, String[] columns) throws StrategyException {
        if(headerName.size() != columns.length) {
            throw new StrategyException("字段解析异常 文件头和文件内容数量不匹配！");
        }
        int userIdIdx = headerName.indexOf("app_user_id");
        int mobileIdx = headerName.indexOf("mobile");
        int registerTimeIdx = headerName.indexOf("register_time");
        int innerAppIdx = headerName.indexOf("inner_app");
        int appIdx = headerName.indexOf("app");
        int abNumIdx = headerName.indexOf("ab_num");
        int appUserIdLast2Idx = headerName.indexOf("app_user_id_last2");
        int runVersionIdx = headerName.indexOf("run_version");
        if(userIdIdx == -1 || mobileIdx == -1 || registerTimeIdx == -1 || innerAppIdx == -1 || appIdx == -1
                || abNumIdx == -1 || appUserIdLast2Idx == -1 || runVersionIdx == -1) {
            throw new StrategyException("字段解析异常 字段缺失！" + headerName);
        }
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setUserId(Long.parseLong(columns[userIdIdx]));
        crowdDetailDo.setCrowdId(crowdId);
        crowdDetailDo.setMobile(AesUtil.mobileDecrypt(columns[mobileIdx], config.getAdsMobileAesKey()));
        crowdDetailDo.setApp(columns[appIdx]);
        crowdDetailDo.setInnerApp(columns[innerAppIdx]);
        crowdDetailDo.setRegisterTime(LocalDateTime.parse(columns[registerTimeIdx], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ssX")));
        crowdDetailDo.setAppUserIdLast2(Integer.parseInt(columns[appUserIdLast2Idx]));
        crowdDetailDo.setAbNum(columns[abNumIdx]);
        crowdDetailDo.setOssRunVersion(columns[runVersionIdx]);

        return crowdDetailDo;
    }

    /**
     * TODO 单人群包分批读取人群数据
     * @param crowdInfoVersionDo
     * @return 人群明细列表，总分批数，当前批次
     */
    public List<CrowdDetailDo> readByCrowdInfo(CrowdInfoVersionDo crowdInfoVersionDo) {

        try {
            // 读取文件首行，确定字段顺序
            String fileUri = crowdInfoVersionDo.getCrowdOssFolder() + crowdInfoVersionDo.getCrowdOssFile();
            OssContentResponse ossContentResponse = rangeReadFileService.readByRange(fileUri, 0L, 500L);

//            List<String> lineList = rangeReadFileService.readByRange(crowdSliceDo.getOssUri(), crowdSliceDo.getStartPos(),
//                    crowdSliceDo.getEndPos());
        } catch (Exception e) {
            log.info("readBySliceExecLog error", e);
        }
        return null;
    }

    public void filterNewRandom(CrowdContext crowdContext, List<CrowdDetailDo> crowdDetailList) {
        if(crowdContext == null) {
            log.info("crowdContext is null");
            return;
        }
        List<String> includeLabelList = crowdContext.getIncludeCustomLabel();
        List<String> excludeLabelList = crowdContext.getExcludeCustomLabel();
        if (!CollectionUtils.isEmpty(includeLabelList)) {
            includeLabelList.forEach(item -> randomNumService.ossCrowdFilterNewRandom(crowdContext, item, crowdDetailList, CrowdLabelEnum.INCLUDE_LABEL.getCode()));
        }

        if (!CollectionUtils.isEmpty(excludeLabelList)) {
            excludeLabelList.forEach(item -> randomNumService.ossCrowdFilterNewRandom(crowdContext, item, crowdDetailList, CrowdLabelEnum.EXCLUDE_LABEL.getCode()));

        }
    }

}
