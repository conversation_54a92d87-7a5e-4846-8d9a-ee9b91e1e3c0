package com.xftech.cdp.infra.rabbitmq.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Data
public class BizEventVO {

    /**用户是否转换 0-否 1-是*/
    private Integer userConvert;

    private String messageId;

    private String bizEventType;

    private Long appUserId;

    private String mobile;

    private String app;

    private String os;

    private String innerApp;

    private String utmSource;

    private Long creditUserId;

    private String deviceId;

    private String ip;

    /**
     * 提额后可用额度
     */
    private BigDecimal amount;

    /**
     * 提额金额
     */
    private BigDecimal adjustChange;

    /**
     * 提额金额
     */
    private BigDecimal adjustAmount;

    /**
     * 当前渠道
     */
    private String currentUtmSource;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 订单号
     * <p>
     * biz_event_type：SettleSuccess
     */
    private String orderNumber;

    /**
     * 还款类型
     * <p>
     * 0自动扣款，1主动还款，2线下还款，3管理员扣款
     * biz_event_type：SettleSuccess
     */
    private Integer repayType;

    /**
     * 是否结清,true 是结清，false 不是结清
     * <p>
     * biz_event_type：SettleSuccess
     */
    @JsonProperty("is_settle")
    @JSONField(name = "is_settle")
    private Boolean isSettle;

    //SettleSuccess
    private String fundSource;
    private String payType;
    private String payChannel;
    private String bizPaymentNumber;
    /**
     * 扣款单状态.   03-成功,04-失败
     * biz_event_type：RepaySuccess
     */
    @JsonProperty("deduction_status")
    @JSONField(name = "deduction_status")
    private String deductionStatus;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 策略ID
     */
    private Long strategyId;

    private Integer strategyType;

    private String engineCode;

    /**
     * 执行Id
     */
    private String strategyExecId;
    /**
     * 分组ID
     */
    private Long strategyGroupId;
    /**
     * 渠道
     */
    private Integer marketChannel;
    /**
     * 人群包ID
     */
    private Long crowdPackId;
    /**
     * 随机数
     */
    private String abNum;
    /**
     * UID后两位
     */
    private Integer appUserIdLast2;
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 决策结果
     */
    private Boolean decisionResult = false;

    /**
     * 失败原因码
     */
    private Integer failCode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;


    private Integer triggerDateTimeHms;

    /**
     * 决策时间
     */
    private LocalDateTime decisionTime;

    /**
     * BizEventMessageVO.ExtrData 对应的JSON串
     */
    private String extrData;

    /**
     * 日志traceId
     */
    private String traceId;

    /**
     * 规则命中结果集
     */
    private List<HitResult> hitResultList;

    /**
     * 重试次数
     */
    private Integer retryNum;

    private String engineDetail;

    private Long engineCallerCount;

    // 渠道id
    private Long marketChannelId;

    private String loanFailReason;
    private String loanFailReasonDetail;

    // 对接事件 RiskActivation
    private String activationStatus;

    //对接事件 RiskTempCredit
    private String temporaryQuotaEffectiveTime;
    private String temporaryQuotaInvalidTime;

    //对接的事件 LoanSuccess
    private Integer firstLoan;
    private String fundSourceType;
    private Integer withProfit;
    private String profitServiceType;
    private BigDecimal profitAmount;
    private BigDecimal first_loan_suc_amt;
    private String cnlPdCode;
    private String status;
    private String failedReason;
    private Integer createTimeHms;
    private Integer loanSuccessTimeHms;

    //对接事件 LoanCommit
    private String innerAppChannelType;
    private Integer loanCommitTimeHms;
    //对接rocket事件 UserLogOff
    private String source;

    private String groupName;

    //是否进入引擎
    private Boolean ifIntoEngine;

    //决策结果是否营销
    private Boolean ifMarket;

    private List<EngineChannelInfo> engineChannelInfoList;

    // 触达类型: MKT-营销,  NOTIFY-通知
    private String dispatchType;
    // 对接提额通知
    // 对接提额通知事件
    private String increaseAmtStrategyId;
    // 提额状态, 1-成功 0-失败
    private Integer increaseAmtStatus;
    //对接生活权益通知
    private Long lifeRightsCallBackStrategyId;
    //生活权益回执状态:1-成功 0-失败
    private Integer lifeRightCallBackStatus;

    //对接X天免息通知
    private Long xDayInterestFreeCallBackStrategyId;
    //X天免息回执状态:1-成功 0-失败
    private Integer xDayInterestFreeCallBackStatus;

    //对接mq事件：ExtractQuotaCardGuide
    private String useManageType;
    private String priceGroup;
    private BigDecimal expectedAdvanceAmount;
    private String adjustmentStatus;
    private Integer extractQuotaCardPurchased;
    private Integer apiBatchAdjOutput;
    private Integer ifExtractQuotaCardTrigger;
    //对接mq事件：orderPayMember
    private String orderPayStatus;
    private Integer currentDeductNumber;
    private Integer completeTimeDistanceFirstDay;
    private Integer completeTimeHms;
    private BigDecimal orderPayAmount;
    //AppLoginIncreaseCredit
    private BigDecimal appLoginIncreaseAvailableAmount;
    private BigDecimal appLoginIncreaseAdjustAmount;
    //CxhRepayXyf01IncreaseCredit
    private String cxhRepayXyf01IncreaseAdjustResult;
    private BigDecimal cxhRepayXyf01IncreaseAdjustAmount;
    //rcsptIncreaseCredit
    @JSONField(name = "manage_type")
    @JsonProperty("manage_type")
    private String manageType;
    private String increaseCreditResult;
    private BigDecimal availableAmount;

    //对接mq事件：vcSign
    private Integer vcSignType;
    private Integer vcSignCardType;
    private Integer vcSignIsRenew;
    private Integer vcDeductTimeHms;
    private BigDecimal vcSignOrderPrice;
    // 是否参与续费活动
    private Integer isRenewActivity;
    // 是否领取vip资金权益
    private Integer isVipFund;

    //对接 LendtradeDerate
    private BigDecimal oriLoanAmt;
    private BigDecimal loanAmt;
    private Integer expirationHour;
    private String freezeType;

    /* 撞库 */
    private Integer credentialStuffingResult;
    //api撞库
    private Integer apiCredentialStuffingResult;
    @JsonProperty("check_type")
    @JSONField(name = "check_type")
    private Integer checkType;
    @JsonProperty("check_time")
    @JSONField(name = "check_time")
    private Date checkTime;
    /**
     * 是否有放款成功记录，0-无，1-有；
     */
    private Integer hasLoanSuccRecord;

    /**
     * 数据来源，1-API渠道实时请求，2-离线撞库
     */
    private Integer dataSource;
    /* 分发授信成功事件 */
    private String product;
    private String orderNo;

    /** 事件扩展参数 */
    private Map<String,Object> ext;

    //风控禁申事件 RcsptRiskAccessControl
    private String bizType;
    private String loanType;
    private Integer forbiddenApplyDay;
    private String diversionLabel;
    private String accessAction;//禁申动作 0禁申，1解除禁申
    //月月惊喜权益发放成功 事件 surpriseRight
    private String surpriseRightType;//权益类型 1-超级提现机会；2-送提额卡；3-VIP特批资金；4-金融券；5-消费券
    private BigDecimal surpriseRightLimitAmtCardAmount;//提额卡金额（单位分）
    private BigDecimal surpriseRightVipFund;// vip特批资金金额（单位分）
    private String surpriseRightCashCouponName;//金融券名称
    private String surpriseRightConsumeCouponName;//消费券名称

    @JSONField(name = "credit_message_type")
    @JsonProperty("credit_message_type")
    String creditMessageType;
    /**
     * 调额结果 S:成功,F:失败
     */
    @JSONField(name = "adjust_result")
    @JsonProperty("adjust_result")
    String adjustResult;

    //(新)注册事件含权益落地页注册上报数据用户注册来源-营销落地页注册xc_mk = 1 为是
    private Integer userRegisterSource;
    private Integer equityType;

    /**
     * 预借款场景：PRE_LEND_NEW:预借款单-新客,PRE_LEND_OLD:预借款单-老客
     */
    private String advanceLoanScene;
    private BigDecimal distributeCurrentAvailableTotalQuota;

    /**
     * AI通话事件-外呼状态
     */
    private String callStatus;

    //API进件事件 API进件结果
    private Integer apiEntryResult;

    /**
     * 用户收到优惠劵事件券类型 1:借款免息券 2:还款立减金 3:限时提额券
     */
    private Integer couponType;
    /**
     * 券归属方1：首贷运营2：复贷运营3：客服4：变现运营5：产品测试
     */
    private Integer couponBelongsTo;

    //电销入库事件 入库结果1：入库成功2：入库失败
    private Integer inventoryStatus;

    //订单风险通过事件 风险通过时间
    private Integer riskPassedTimeHms;
    @Data
    public static class EngineChannelInfo {
        //引擎返回的groupId
        private String groupId;
        //引擎返回的渠道
        private Integer marketChannel;
        //引擎返回的渠道对应模板id
        private String templateId;
    }

    public void addDecisionFailResult(DecisionResultEnum decisionResultEnum) {
        this.decisionResult = false;
        this.failCode = decisionResultEnum.getFailCode();
        this.failReason = decisionResultEnum.getFailReason();
    }

    public void addDecisionSucResult() {
        this.decisionResult = true;
    }

    public void addHitResult(String expression, Map<String, Object> paramMap, Boolean hit) {
        HitResult hitResult = new HitResult();
        hitResult.setHit(hit);
        hitResult.setExpression(expression);
        hitResult.setExpParam(paramMap.toString());
        if (hitResultList == null) {
            hitResultList = new ArrayList<>();
        }
        hitResultList.add(hitResult);
    }

    public void addEngineDetail(String key, Object value) {
        Map detail = null;
        if (StringUtils.isEmpty(engineDetail)) {
            detail = new HashMap<>();
        } else {
            detail = JsonUtil.parse(engineDetail, Map.class);
        }
        detail.put(key, value);
        engineDetail = JsonUtil.toJson(detail);
    }

    /* 2025.04.10 业务引擎-延迟决策 */
    // 引擎延迟决策ID
    private Long engineReDecisionId;

}
