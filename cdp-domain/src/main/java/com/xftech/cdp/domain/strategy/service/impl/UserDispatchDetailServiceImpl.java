package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Lists;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.external.DispatchRecordQueryReq;
import com.xftech.cdp.api.dto.req.external.ExistDispatchRecordReq;
import com.xftech.cdp.api.dto.req.external.IncreaseAmtCallbakRequest;
import com.xftech.cdp.api.dto.req.external.TeleImportResultReq;
import com.xftech.cdp.api.dto.resp.external.DispatchRecordQueryResp;
import com.xftech.cdp.api.dto.resp.external.ExistDispatchRecordResp;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushQueryStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.event.EventMessageProcessor;
import com.xftech.cdp.domain.flowctrl.model.dto.UserDispatchIndexDto;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dto.NotifyEventDto;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchDetailDto;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.UserDispatchDetailStatusEnum;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.domain.strategy.service.UserSendCounterService;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.feign.service.DataFeatureService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsRequest;
import com.xftech.cdp.infra.client.ads.model.req.ChannelIndexReq;
import com.xftech.cdp.infra.client.ads.model.req.StrategyIndexReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.ads.model.resp.UserDispatchResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.thread.DispatchQueryDBExecutor;
import com.xftech.cdp.infra.utils.*;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum.VOICE_NEW;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/28 9:23
 */
@Slf4j
@Service
public class UserDispatchDetailServiceImpl implements UserDispatchDetailService {

    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AdsClient adsClient;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private CrowdPushBatchService crowdPushBatchService;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserDispatchFailDetailService userDispatchFailDetailService;
    @Autowired
    private Config config;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private UserSendCounterService userSendCounterService;
    @Autowired
    private AppConfigService appConfigService;

    @Autowired
    private MqTemplate mqTemplate;

    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private EventMessageProcessor eventMessageProcessor;
    @Autowired
    private DataFeatureService dataFeatureService;

    /**
     * 全局渠道
     */
    private static final String GLOBAL_CHANNEL_CNT = "mq_global_channel_cnt";
    /**
     * 业务线渠道
     */
    private static final String GLOBAL_CHANNEL_BIZ_TYPE_CNT = "mq_global_channel_biz_type_cnt";
    /**
     * 策略ID渠道
     */
    private static final String GLOBAL_CHANNEL_STRATEGY_ID_CNT = "mq_global_channel_strategy_id_cnt";

    /**
     * 查询指定批次的用户下发明细
     *
     * @param tableNameNo   明细表序号
     * @param marketChannel 渠道
     * @param batchNum      批次号
     * @return 用户下发明细
     */
    @Override
    public List<CrowdDetailDo> selectListByBatchNo(String tableNameNo, Integer marketChannel, String batchNum) {
        List<UserDispatchDetailDo> list = userDispatchDetailRepository.selectListByBatchNo(tableNameNo, marketChannel, batchNum);
        return list.stream().map(userDispatchDetailDo -> {
            CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
            crowdDetailDo.setCrowdId(userDispatchDetailDo.getCrowdPackId());
            crowdDetailDo.setUserId(userDispatchDetailDo.getUserId());
            return crowdDetailDo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取用户流控指标
     *
     * @param tableNameNo 当前下发明细用户所有表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID集合
     * @return 用户流控指标
     */
    @Override
    public List<UserDispatchIndexDto> getUserIndex(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {
        log.info("开始查询用户触达明细 用户数量：{}", userIdList.size());
        long startTime = Instant.now().toEpochMilli();
        Map<Long, Integer> indexMap = this.queryIndex(tableNameNo, triple, userIdList, statusList);
        List<UserDispatchIndexDto> userDispatchIndexDtoList = userIdList.stream().map(userId -> {
            UserDispatchIndexDto userDispatchIndexDto = new UserDispatchIndexDto();
            userDispatchIndexDto.setUserId(userId);
            userDispatchIndexDto.setCount(Optional.ofNullable(indexMap.get(userId)).orElse(0));
            return userDispatchIndexDto;
        }).collect(Collectors.toList());
        log.info("结束查询用户触达明细 查询方式:数仓 明细表序号:{} 用户数量:{} 耗时:{}ms", tableNameNo, userIdList.size(), Instant.now().toEpochMilli() - startTime);
        return userDispatchIndexDtoList;
    }

    /**
     * 获取用户流控指标
     * TODO 使用redis缓存优化月底性能
     *
     * @param tableNameNo 当前下发明细用户所有表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID集合
     * @return 用户流控指标
     */
    @Override
    public List<UserDispatchIndexDto> getUserIndexNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {

        long startTime = Instant.now().toEpochMilli();
        Map<Long, Integer> indexMap = this.queryIndexNew(tableNameNo, triple, userIdList, statusList);
        List<UserDispatchIndexDto> userDispatchIndexDtoList = userIdList.stream().map(userId -> {
            UserDispatchIndexDto userDispatchIndexDto = new UserDispatchIndexDto();
            userDispatchIndexDto.setUserId(userId);
            userDispatchIndexDto.setCount(Optional.ofNullable(indexMap.get(userId)).orElse(0));
            return userDispatchIndexDto;
        }).collect(Collectors.toList());
        log.info("结束查询用户触达明细new 查询方式:数仓 明细表序号:{} 用户数量:{} 耗时:{}ms", tableNameNo, userIdList.size(), Instant.now().toEpochMilli() - startTime);
        return userDispatchIndexDtoList;
    }

    /**
     * 从数仓查询用户触达指标
     *
     * @param tableNameNo 表序号
     * @param triple      策略执行信息 left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  当前批次用户
     * @return 用户触达指标
     */
    private Map<Long, Integer> queryIndex(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {
        FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(triple.getRight().getType());
        Map<Long, Integer> indexMap = userIdList.stream().collect(Collectors.toMap(Function.identity(), item -> 0));
        for (List<Long> list : Lists.partition(userIdList, strategyConfig.getDetailBatchSize())) {

            Map<Long, Integer> localIndexMap = this.getLocalIndexMap(tableNameNo, triple, list, statusList);
            localIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));

            if (triple.getRight().getLimitDays() <= 1) {
                log.warn("当前流控规则[{}]配置的是每1天{}次，无需调用数仓接口进行查询", triple.getRight().getId(), triple.getRight().getLimitTimes());
                continue;
            }

            Map<Long, Integer> adsIndexMap = this.getAdsIndexMap(flowCtrlTypeEnum, triple, list);
            adsIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));
        }
        return indexMap;
    }

    /**
     * 从数仓查询用户触达指标
     *
     * @param tableNameNo 表序号
     * @param triple      策略执行信息 left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  当前批次用户
     * @return 用户触达指标
     */
    private Map<Long, Integer> queryIndexNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {
        FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(triple.getRight().getType());
        Map<Long, Integer> indexMap = userIdList.stream().collect(Collectors.toMap(Function.identity(), item -> 0));
        for (List<Long> list : Lists.partition(userIdList, strategyConfig.getDetailBatchSize())) {
            if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + triple.getLeft(), "newFlowCtrlRuleSwitch")) {
                Map<Long, Integer> localIndexMap = this.getLocalIndexMapTwoDay(tableNameNo, triple, list, statusList);
                localIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));

                if (triple.getRight().getLimitDays() <= 2) {
                    log.info("queryIndexNew-当前流控规则:{} 配置的是每{}天{}次 无需特征平台", triple.getRight().getId(),
                            triple.getRight().getLimitDays(), triple.getRight().getLimitTimes());
                    continue;
                }
                // 使用特征平台新接口
                Map<Long, Integer> adsIndexMap = this.getFeatureIndexMap(flowCtrlTypeEnum, triple, list);
                adsIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));
            } else {
                Map<Long, Integer> localIndexMap = this.getLocalIndexMapNew(tableNameNo, triple, list, statusList);
                localIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));

                if (triple.getRight().getLimitDays() <= 1) {
                    log.info("queryIndexNew-当前流控规则:{} 配置的是每1天{}次 无需数仓接口进行查询", triple.getRight().getId(), triple.getRight().getLimitTimes());
                    continue;
                }

                Map<Long, Integer> adsIndexMap = this.getAdsIndexMap(flowCtrlTypeEnum, triple, list);
                adsIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));
            }
        }
        return indexMap;
    }

    /**
     * 从本地库查询用户当天触达次数指标
     *
     * @param tableNameNo 明细表序号
     * @param triple      流控规则 left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID
     * @return 当天触达次数指标
     */
    private Map<Long, Integer> getLocalIndexMap(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {
        List<UserDispatchDetailDto> indexList = userDispatchDetailRepository.getIndexCount(tableNameNo, triple, userIdList, statusList);
        return indexList.stream().collect(Collectors.toMap(UserDispatchDetailDto::getUserId, UserDispatchDetailDto::getCount));
    }

    /**
     * 从本地库查询用户当天触达次数指标
     * TODO 依赖redis缓存优化性能
     * 查两天的数据(跨月需要查询两次)
     * @param tableNameNo 明细表序号
     * @param triple      流控规则 left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIdList  用户ID
     * @return 当天触达次数指标
     */
    private Map<Long, Integer> getLocalIndexMapNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {
        List<UserDispatchDetailDto> indexList = userDispatchDetailRepository.getIndexCountNew(tableNameNo, triple, userIdList, statusList);
        return indexList.stream().collect(Collectors.toMap(UserDispatchDetailDto::getUserId, UserDispatchDetailDto::getCount));
    }

    private Map<Long, Integer> getLocalIndexMapTwoDay(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList, List<Integer> statusList) {

        List<UserDispatchDetailDto> indexList = Collections.emptyList();
        List<UserDispatchDetailDto> lastMonthIndexList = Collections.emptyList();
        boolean isLogEnable = WhitelistSwitchUtil.boolSwitchByApollo("getLocalIndexMapTwoDayLogEnable");

        // 获取当前日期
        LocalDate currentDate = getCurrentDate();
        if (currentDate.getDayOfMonth() == 1) {
            if(isLogEnable) {
                log.info("getLocalIndexMapTwoDay 当前是day1");
            }
            // 当月的第一天
            if(triple.getRight().getLimitDays() >= 2) {
                // 单独查上个月最后一天的数据
                String lastMonthTableNo = LocalDateTimeUtil.format(currentDate.minusMonths(1), "yyyyMM");
                LocalDateTime startDateTimeLastMonth = LocalDateTime.of(currentDate.minusDays(1), LocalTime.MIN);
                LocalDateTime endDateTimeLastMonth = LocalDateTime.of(currentDate.minusDays(1), LocalTime.MAX);
                if(isLogEnable) {
                    log.info("getLocalIndexMapTwoDay 单独查上个月最后一天的数据 strategyId:{} limitDays:{} limitTimes:{} startDateTime:{} endDateTime:{}",
                            triple.getLeft(), triple.getRight().getLimitDays(), triple.getRight().getLimitTimes(), startDateTimeLastMonth, endDateTimeLastMonth);
                }
                lastMonthIndexList = userDispatchDetailRepository.getIndexCountNewWithDate(lastMonthTableNo, triple,
                        userIdList, statusList, startDateTimeLastMonth, endDateTimeLastMonth);
            }
            String curMonthTableNo = LocalDateTimeUtil.format(currentDate, "yyyyMM");
            LocalDateTime startDateTime = LocalDateTime.of(currentDate, LocalTime.MIN);
            LocalDateTime endDateTime = LocalDateTime.of(currentDate, LocalTime.MAX);
            if(isLogEnable) {
                log.info("getLocalIndexMapTwoDay 查当月第一天的数据 strategyId:{} limitDays:{} limitTimes:{} startDateTime:{} endDateTime:{}",
                        triple.getLeft(), triple.getRight().getLimitDays(), triple.getRight().getLimitTimes(), startDateTime, endDateTime);
            }
            indexList = userDispatchDetailRepository.getIndexCountNewWithDate(curMonthTableNo, triple,
                    userIdList, statusList, startDateTime, endDateTime);

            if(!CollectionUtils.isEmpty(lastMonthIndexList)) {
                // 合并两天的数据
                indexList.addAll(lastMonthIndexList);
            }
        } else {

            String curMonthTableNo = LocalDateTimeUtil.format(currentDate, "yyyyMM");
            LocalDateTime startDateTime = LocalDateTime.of(currentDate, LocalTime.MIN);
            LocalDateTime endDateTime =  LocalDateTime.of(currentDate, LocalTime.MAX);
            if(triple.getRight().getLimitDays() >= 2) {
                startDateTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN);
            }
            if(isLogEnable) {
                log.info("getLocalIndexMapTwoDay 直接查最多两天的数据 strategyId:{} limitDays:{} startDateTime:{} endDateTime:{}",
                        triple.getLeft(), triple.getRight().getLimitDays(), startDateTime, endDateTime);
            }
            indexList = userDispatchDetailRepository.getIndexCountNewWithDate(curMonthTableNo, triple,
                    userIdList, statusList, startDateTime, endDateTime);
        }

        // 合并两天的数据
        Map<Long, Integer> mapResult = indexList.stream().collect(Collectors.toMap(UserDispatchDetailDto::getUserId, UserDispatchDetailDto::getCount, Integer::sum));
        if(isLogEnable) {
            log.info("getLocalIndexMapTwoDay 查询用户 {} 历史触达次数, 查询结果 {}", userIdList, mapResult);
        }
        return mapResult;
    }

    private LocalDate getCurrentDate() {
        String currentDateStr = ApolloUtil.getAppProperty("flowctrl.twoDayQuery.currentDate");
        if(StringUtils.isNotBlank(currentDateStr)) {
            try {
                log.info("当前日期从apollo获取:{}", currentDateStr);
                LocalDate curDate = LocalDate.parse(currentDateStr);
                return curDate;
            } catch (Exception e) {
                log.error("当前日期解析失败:{}", e.getMessage());
            }
        }
       return LocalDateTime.now().toLocalDate();
    }

    /**
     * 从数仓查询用户触达次数指标
     *
     * @param flowCtrlTypeEnum 流控类型
     * @param triple           流控规则
     * @param userIdList       用户ID
     * @return 用户触达次数指标
     */
    private Map<Long, Integer> getAdsIndexMap(FlowCtrlTypeEnum flowCtrlTypeEnum, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList) {
        try {
            BaseAdsRequest<?> req = new BaseAdsRequest<>();
            Long strategyId = triple.getLeft();
            if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
                log.info("新流控开关打开，使用新接口参数查询用户触达次数指标");
                req = this.getReqParamNew(flowCtrlTypeEnum, triple, userIdList);
            } else {
                req = this.getReqParam(flowCtrlTypeEnum, triple, userIdList);
            }

            BaseAdsResponse<List<UserDispatchResp>> response = adsClient.queryUserDispatchCnt(req);
            return !response.isSuccess() ? new HashMap<>() : response.getPayload().stream()
                    .map(UserDispatchResp::getParams)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(UserDispatchResp.Params::getAppUserId, UserDispatchResp.Params::getDispatchCnt));
        } catch (Exception e) {
            log.warn("数仓流控指标查询异常", e);
            return new HashMap<>(16);
        }
    }

    /**
     * 查询特征平台新接口获取用户历史触达次数
     * https://devops.aliyun.com/projex/project/c253bf4a010af1407b30cb9a87/task/f53787118e68510a3a357fcd3e
     * @param flowCtrlTypeEnum 当前流控类型
     * @param triple
     * @param userIdList
     * @return
     */
    private Map<Long, Integer> getFeatureIndexMap(FlowCtrlTypeEnum flowCtrlTypeEnum, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList) {
        try {
            Map<Long, Integer> userCntMap = new HashMap<>();

            LocalDate endDate = LocalDate.now().minusDays(2);
            LocalDate startDate = LocalDate.now().minusDays(triple.getRight().getLimitDays() - 1);
            Map<String, Object> inputs = new HashMap<>();
            inputs.put("market_channel", "" + triple.getMiddle());
            inputs.put("start_time", LocalDateTimeUtil.format(startDate, "yyyy-MM-dd"));
            inputs.put("end_time", LocalDateTimeUtil.format(endDate, "yyyy-MM-dd"));
            List<String> featureList = null;
            if(FlowCtrlTypeEnum.CHANNEL.equals(flowCtrlTypeEnum)) { // 全局渠道
                featureList = Arrays.asList(GLOBAL_CHANNEL_CNT);
            } else if (FlowCtrlTypeEnum.STRATEGY.equals(flowCtrlTypeEnum)) { // 策略渠道
                featureList = Arrays.asList(GLOBAL_CHANNEL_STRATEGY_ID_CNT);
                inputs.put("strategy_ids", "" + triple.getLeft());
            } else if (FlowCtrlTypeEnum.BIZ_LINE.equals(flowCtrlTypeEnum)) { // 业务线渠道
                featureList = Arrays.asList(GLOBAL_CHANNEL_BIZ_TYPE_CNT);
                inputs.put("biz_type", getBizType(triple.getLeft()));
            } else {
                log.error("当前流控类型[{}]不支持特征平台新接口", flowCtrlTypeEnum.getType());
                throw new Exception("当前流控类型[" + flowCtrlTypeEnum.getType()+ "]不支持特征平台新接口");
            }

            for (Long userId : userIdList) {
                // 可不做多线程，实际上游传参没有多用户场景
                inputs.put("app_user_id", userId);
                FeatureQueryResponse response = dataFeatureService.getDateFeatureResponse(inputs, featureList);
                if(response != null && response.isSuc() && response.getFeatureValues() != null) {
                    Map<String, FeatureQueryResponse.FeatureValueModel> newFeaturMap = response.getFeatureValues();
                    if (newFeaturMap.containsKey(featureList.get(0))) {
                        Integer cnt = (Integer) newFeaturMap.get(featureList.get(0)).getObj();
                        if(cnt != -9999) { // 默认值 -9999，表示没有数据或查询失败
                            userCntMap.put(userId, cnt);
                        }
                    }
                }
            }
            return userCntMap;
        } catch (Exception e) {
            log.warn("特征平台流控指标查询异常", e);
            return new HashMap<>(16);
        }
    }


    /**
     * 获取请求参数
     *
     * @param flowCtrlTypeEnum 流控类型
     * @param triple           策略执行信息
     * @param userIdList       当前批次用户
     * @return 数仓请求参数
     */
    private BaseAdsRequest<?> getReqParam(FlowCtrlTypeEnum flowCtrlTypeEnum, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList) {
        // 开始/结束时间
        LocalDate endDate = LocalDate.now().plusDays(-1);
        LocalDate startDate = LocalDate.now().plusDays(-(triple.getRight().getLimitDays() - 1));

        StrategyIndexReq strategyReq = new StrategyIndexReq(userIdList, Arrays.asList(getEffectiveValue(triple).split(",")), StrategyMarketChannelEnum.getFlcCodes(), startDate, endDate);
        ChannelIndexReq channelReq = new ChannelIndexReq(userIdList, Arrays.asList(getEffectiveValue(triple).split(",")), startDate, endDate);
        return new BaseAdsRequest<>(flowCtrlTypeEnum == FlowCtrlTypeEnum.MULTI_STRATEGY || flowCtrlTypeEnum == FlowCtrlTypeEnum.STRATEGY ? strategyReq : channelReq);
    }

    /**
     *
     * @param flowCtrlTypeEnum
     * @param triple
     * @param userIdList
     * @return
     */
    private BaseAdsRequest<?> getReqParamNew(FlowCtrlTypeEnum flowCtrlTypeEnum, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIdList) {
        // 开始/结束时间
        LocalDate endDate = LocalDate.now().plusDays(-1);
        LocalDate startDate = LocalDate.now().plusDays(-(triple.getRight().getLimitDays() - 1));


        if (flowCtrlTypeEnum == FlowCtrlTypeEnum.MULTI_STRATEGY || flowCtrlTypeEnum == FlowCtrlTypeEnum.STRATEGY) {
            Integer channelType = triple.getMiddle();
            List<Integer> channelList = Arrays.asList(channelType);
            StrategyIndexReq strategyReq = new StrategyIndexReq(userIdList, Arrays.asList(getEffectiveValue(triple).split(",")), channelList, startDate, endDate);
            return new BaseAdsRequest<>(strategyReq);
        } else {
            ChannelIndexReq channelReq = new ChannelIndexReq(userIdList, Arrays.asList(getEffectiveValue(triple).split(",")), startDate, endDate);
            return new BaseAdsRequest<>(channelReq);
        }
    }

    /**
     * 获取生效内容
     *
     * @param triple 流控规则
     * @return 生效内容
     */
    private String getEffectiveValue(Triple<Long, Integer, FlowCtrlDo> triple) {
        FlowCtrlDo flowCtrlDo = triple.getRight();
        FlowCtrlTypeEnum typeEnum = FlowCtrlTypeEnum.getInstance(flowCtrlDo.getType());
        // 多策略共享
        if (typeEnum == FlowCtrlTypeEnum.MULTI_STRATEGY) {
            return flowCtrlDo.getEffectiveStrategy();
        }
        // 策略纬度
        else if (typeEnum == FlowCtrlTypeEnum.STRATEGY) {
            String effectiveStrategy = flowCtrlDo.getEffectiveStrategy();
            return StringUtils.equals(effectiveStrategy, "0") ? "999999999" : String.valueOf(triple.getLeft());
        }
        // 渠道纬度
        else {
            String effectiveChannel = flowCtrlDo.getEffectiveChannel();
            return StringUtils.equals(effectiveChannel, "0") ? "999" : String.valueOf(triple.getMiddle());
        }
    }

//    /**
//     * 批量报销下发明细
//     *
//     * @param tableNameNo 表序号
//     * @param list        用户集合
//     */
//    @Override
//    public void saveBatch(String tableNameNo, List<UserDispatchDetailDo> list) {
//        userDispatchDetailRepository.saveBatch(tableNameNo, list);
//    }

    public void saveBatchWithoutTx(String tableNameNo, List<UserDispatchDetailDo> list, StrategyRulerEnum strategyRulerEnum) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        userDispatchDetailRepository.saveBatchWithoutTx(tableNameNo, list);
        if (StrategyRulerEnum.EVENT == strategyRulerEnum) {
            for (UserDispatchDetailDo userDispatchDetailDo : list) {
                statDispatchNum(userDispatchDetailDo);
            }
        }
    }

    /**
     * 保存策略下发明细
     *
     * @param dispatchDto 初始化参数
     * @param batchNum    批次号
     * @param app         app
     * @param innerApp    innerApp
     * @param list        批量参数
     * @param detailList  人群明细
     * @param resp        接口响应
     */
    @Override
    public <T> List<UserDispatchDetailDo> saveDispatchDetail(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo, String batchNum, String app, String innerApp, List<T> list, List<CrowdDetailDo> detailList, Pair<String, String> resp) {
        LocalDateTime now = LocalDateTime.now();
        AtomicReference<CrowdPushBatchDo> pushBatchDo = new AtomicReference<>(crowdPushBatchDo);
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>(8);
        TransactionUtil.transactional(() -> {
            crowdPushBatchService.insert(dispatchDto, crowdPushBatchDo, app, innerApp, list, resp.getLeft(), resp.getRight(), batchNum);
            detailList.stream().forEach(crowdDetailDo -> {
                UserDispatchDetailDo dispatchDetailDo = new UserDispatchDetailDo();
                dispatchDetailDo.setUserId(crowdDetailDo.getUserId());
                dispatchDetailDo.setBatchNum(pushBatchDo.get().getBatchNum());
                dispatchDetailDo.setCrowdPackId(crowdDetailDo.getCrowdId());
                dispatchDetailDo.setStrategyId(pushBatchDo.get().getStrategyId());
                dispatchDetailDo.setStrategyChannelId(pushBatchDo.get().getStrategyMarketChannelId());
                dispatchDetailDo.setMarketChannel(pushBatchDo.get().getMarketChannel());
                dispatchDetailDo.setStrategyExecId(LocalDateTimeUtil.format(LocalDate.now(), "yyMMdd") + String.format("%010d", pushBatchDo.get().getStrategyId()));
                dispatchDetailDo.setExecLogId(pushBatchDo.get().getStrategyExecLogId());
                dispatchDetailDo.setStatus(UserDispatchDetailStatusEnum.INIT.getStatus());
                dispatchDetailDo.setDispatchTime(now);
                dispatchDetailDo.setTriggerDatetime(now);
                dispatchDetailDo.setCreatedTime(now);
                dispatchDetailDo.setUpdatedTime(now);
                dispatchDetailDo.setStrategyGroupId(dispatchDto.getStrategyGroupId());
                dispatchDetailDo.setStrategyGroupName(dispatchDto.getStrategyGroupName());
                if(StrategyMarketChannelEnum.VOICE_NEW.getCode() == dispatchDto.getStrategyChannel()){
                    if(!StringUtils.isEmpty(dispatchDto.getNameTypeId())){
                        dispatchDetailDo.setTemplateId(dispatchDto.getNameTypeId());
                    }else{
                        dispatchDetailDo.setTemplateId(crowdPushBatchDo.getTemplateId());
                    }
                }else{
                    dispatchDetailDo.setTemplateId(crowdPushBatchDo.getTemplateId());
                }
                dispatchDetailDo.setExtDetail(JsonUtil.toJson(crowdDetailDo.getExtDetailData(dispatchDetailDo.getMarketChannel())));
                dispatchDetailDo.setBizType(getBizType(pushBatchDo.get().getStrategyId()));
                userDispatchDetailDoList.add(dispatchDetailDo);
            });
            saveBatchWithoutTx(dispatchDto.getDetailTableNo(), userDispatchDetailDoList, dispatchDto.getStrategyRulerEnum());
        }, e -> log.warn("策略下发明细保存异常", e));
        return userDispatchDetailDoList;
    }

    public String getBizType(Long strategyId) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        if(strategyDo != null) {
            return strategyDo.getBusinessType();
        }
        return "";
    }

    @Override
    public void saveDispatchDetail(String detailTableNo, StrategyRulerEnum strategyRulerEnum, UserDispatchDetailDo dispatchDetailDo, CrowdPushBatchDo crowdPushBatchDo, CrowdDetailDo crowdDetailDo) {
        LocalDateTime now = LocalDateTime.now();
        dispatchDetailDo.setUserId(crowdDetailDo.getUserId());
        dispatchDetailDo.setBatchNum(crowdPushBatchDo.getBatchNum());
        dispatchDetailDo.setCrowdPackId(crowdDetailDo.getCrowdId());
        dispatchDetailDo.setStrategyId(crowdPushBatchDo.getStrategyId());
        dispatchDetailDo.setBizType(getBizType(crowdPushBatchDo.getStrategyId()));
        dispatchDetailDo.setStrategyGroupId(crowdPushBatchDo.getStrategyGroupId());
        dispatchDetailDo.setStrategyChannelId(crowdPushBatchDo.getStrategyMarketChannelId());
        dispatchDetailDo.setMarketChannel(crowdPushBatchDo.getMarketChannel());
        dispatchDetailDo.setStrategyExecId(LocalDateTimeUtil.format(LocalDate.now(), "yyMMdd") + String.format("%010d", crowdPushBatchDo.getStrategyId()));
        dispatchDetailDo.setExecLogId(crowdPushBatchDo.getStrategyExecLogId());
        dispatchDetailDo.setStatus(UserDispatchDetailStatusEnum.INIT.getStatus());
        dispatchDetailDo.setDispatchTime(now);
        dispatchDetailDo.setTriggerDatetime(now);
        dispatchDetailDo.setCreatedTime(now);
        dispatchDetailDo.setUpdatedTime(now);
        dispatchDetailDo.setTemplateId(crowdPushBatchDo.getTemplateId());
        dispatchDetailDo.setExtDetail(JsonUtil.toJson(crowdDetailDo.getExtDetailData(dispatchDetailDo.getMarketChannel())));
        saveBatchWithoutTx(detailTableNo, Arrays.asList(dispatchDetailDo), strategyRulerEnum);
    }

    @Override
    public EventPushBatchDo saveEventDispatchDetail(DispatchDto reach, EventPushBatchDo eventPushBatch, UserDispatchDetailDo dispatchDetail, String batchNum, CrowdDetailDo crowdDetail, Triple<String, String, String> resp) {
        TransactionUtil.transactional(() -> {
            eventPushBatch.setStrategyId(reach.getStrategyId());
            eventPushBatch.setMarketChannelId(reach.getStrategyChannelId());
            eventPushBatch.setExecLogId(reach.getStrategyExecLogId());
            eventPushBatch.setMarketChannel(reach.getStrategyChannel());
            eventPushBatch.setUserId(crowdDetail.getUserId());
            eventPushBatch.setMobile(crowdDetail.getMobile());
            eventPushBatch.setApp(crowdDetail.getApp());
            eventPushBatch.setTemplateId(Optional.ofNullable(reach.getStrategyMarketChannelTemplateId()).orElse(""));
            eventPushBatch.setBatchNum(batchNum);
            eventPushBatch.setInnerBatchNum(serialNumberUtil.batchNum());
            eventPushBatch.setSendCode(resp.getLeft());
            eventPushBatch.setSendMsg(resp.getMiddle());
            eventPushBatch.setStatus(2);
            eventPushBatch.setDetailTableNo(reach.getDetailTableNo());
            eventPushBatch.setBizEventType(reach.getBizEventType());
            eventPushBatch.setStrategyGroupId(reach.getStrategyGroupId());
            eventPushBatch.setStrategyGroupName(reach.getStrategyGroupName());
            eventPushBatch.setGroupName(dispatchDetail.getGroupName());
            eventPushBatchRepository.insert(reach.getDetailTableNo(), eventPushBatch);

            dispatchDetail.setUserId(crowdDetail.getUserId());
            dispatchDetail.setBatchNum(eventPushBatch.getInnerBatchNum());
            dispatchDetail.setCrowdPackId(crowdDetail.getCrowdId());
            dispatchDetail.setStrategyId(reach.getStrategyId());
            dispatchDetail.setBizType(reach.getBizType()); // 业务线类型 TODO 确认上层是否已处理
            dispatchDetail.setStrategyChannelId(reach.getStrategyChannelId());
            dispatchDetail.setMarketChannel(reach.getStrategyChannel());
            dispatchDetail.setStrategyExecId(reach.getStrategyExecId());
            dispatchDetail.setExecLogId(eventPushBatch.getExecLogId());
            dispatchDetail.setStatus(UserDispatchDetailStatusEnum.INIT.getStatus());
            dispatchDetail.setDispatchTime(LocalDateTime.now());
            dispatchDetail.setMessageId(reach.getMessageId());
            dispatchDetail.setTriggerDatetime(reach.getTriggerDatetime());
            dispatchDetail.setBizEventType(reach.getBizEventType());
            dispatchDetail.setExtDetail(JsonUtil.toJson(crowdDetail.getExtDetailData(dispatchDetail.getMarketChannel())));
            if(VOICE_NEW.getCode() == reach.getStrategyChannel() && !StringUtils.isEmpty(reach.getNameTypeId())){
                dispatchDetail.setTemplateId(reach.getNameTypeId());
            }else{
                dispatchDetail.setTemplateId(eventPushBatch.getTemplateId());
            }
            dispatchDetail.setStrategyGroupId(reach.getStrategyGroupId());
            dispatchDetail.setStrategyGroupName(reach.getStrategyGroupName());
            dispatchDetail.setGroupName(dispatchDetail.getGroupName());

            if (StringUtils.isNotEmpty(reach.getDispatchType())){
                dispatchDetail.setDispatchType(reach.getDispatchType());
            }
            userDispatchDetailRepository.saveBatchWithoutTx(reach.getDetailTableNo(), Collections.singletonList(dispatchDetail));

            if (StrategyRulerEnum.EVENT == reach.getStrategyRulerEnum()) {
                statDispatchNum(dispatchDetail);
            }
        });
        return eventPushBatch;
    }

    private void statDispatchNum(UserDispatchDetailDo dispatchDetail) {
        String curDate = LocalDateTimeUtil.format(dispatchDetail.getTriggerDatetime(), "yyyyMMdd");
        String key = RedisKeyUtils.genStatDecnUserDisp(curDate, dispatchDetail.getStrategyId(), dispatchDetail.getStrategyGroupId());
        redisUtils.pfAddTwoDay(key, dispatchDetail.getUserId());
    }
    @Override
    public Boolean teleCall(TeleImportResultReq req,StrategyMarketChannelEnum strategyMarketChannelEnum){
        try {
            log.info("电销发送结果通知, voice={}", JsonUtil.toJson(req));
            if (CollectionUtils.isEmpty(req.getUserList())) {
                log.info("暂无需要更新的明细记录，批次号：{}", req.getFlowNo());
                return Boolean.TRUE;
            }
            List<UserDispatchDetailDo> dispatchDetailList = req.getUserList().stream().map(teleUser -> {
                UserDispatchDetailDo dispatchDetailDo = new UserDispatchDetailDo();
                dispatchDetailDo.setUserId(teleUser.getCreditId());
                dispatchDetailDo.setBatchNum(req.getFlowNo());
                dispatchDetailDo.setMarketChannel(strategyMarketChannelEnum.getCode());
                dispatchDetailDo.setStatus(Objects.equals(teleUser.getStatus(), 1) ? UserDispatchDetailStatusEnum.SUCCESS.getStatus() : UserDispatchDetailStatusEnum.FAIL.getStatus());
                dispatchDetailDo.setDispatchTime(LocalDateTimeUtil.parse(teleUser.getExecTime(), TimeFormat.DATE_TIME));
                dispatchDetailDo.setUpdatedTime(LocalDateTime.now());
                return dispatchDetailDo;
            }).collect(Collectors.toList());

            CrowdPushBatchDo pushBatch = crowdPushBatchRepository.selectByChannelAndBatchNum(strategyMarketChannelEnum, req.getFlowNo());
            if (Objects.nonNull(pushBatch)) {
                Map<UserDispatchDetailStatusEnum, List<UserDispatchDetailDo>> map = dispatchDetailList.stream().collect(Collectors.groupingBy(item -> UserDispatchDetailStatusEnum.getInstance(item.getStatus())));
                pushBatch.setSuccCount(Optional.ofNullable(map.get(UserDispatchDetailStatusEnum.SUCCESS)).orElse(Lists.newArrayList()).size());
                pushBatch.setFailCount(Optional.ofNullable(map.get(UserDispatchDetailStatusEnum.FAIL)).orElse(Lists.newArrayList()).size());
                pushBatch.setBatchStatus(CrowdPushBatchStatusEnum.FINISH.getCode());
                pushBatch.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
                crowdPushBatchRepository.updateById(pushBatch);

                Optional.ofNullable(strategyExecLogRepository.selectById(pushBatch.getStrategyExecLogId())).ifPresent(execLog -> {
                    List<CrowdPushBatchDo> crowdPushBatchList = crowdPushBatchRepository.selectByStrategyExecLogId(execLog.getId());
                    execLog.setReceiveCount(crowdPushBatchList.stream().map(item -> item.getSuccCount() + item.getFailCount()).reduce(0, Integer::sum));
                    execLog.setSuccCount(crowdPushBatchList.stream().map(CrowdPushBatchDo::getSuccCount).reduce(0, Integer::sum));
                    execLog.setExecStatus(null);
                    strategyExecLogRepository.updateById(execLog);
                });

                Lists.partition(dispatchDetailList, 100).forEach(list -> userDispatchDetailRepository.updateBatch(pushBatch.getDetailTableNo(), list));
            } else {
                EventPushBatchDo eventPushBatch = eventPushBatchRepository.getByChannelAndBatchNum(strategyMarketChannelEnum, req.getFlowNo());
                Optional.ofNullable(eventPushBatch).ifPresent(eventPushBatchDo -> {

                    dispatchDetailList.forEach(userDispatchDetailDo -> userDispatchDetailDo.setBatchNum(eventPushBatchDo.getInnerBatchNum()));
                    Lists.partition(dispatchDetailList, 100).forEach(list -> userDispatchDetailRepository.updateBatch(eventPushBatchDo.getDetailTableNo(), list));

                    String date = null;
                    StrategyExecLogDo execLogDo = strategyExecLogRepository.selectById(eventPushBatch.getExecLogId());
                    if (execLogDo != null) {
                        date = LocalDateTimeUtil.format(execLogDo.getExecTime(), "yyyyMMdd");
                    }
                    List<UserDispatchFailDetailDto.UserInfo> userInfoList = new ArrayList<>();
                    for (UserDispatchDetailDo dispatchDetailDo : dispatchDetailList) {
                        if (UserDispatchDetailStatusEnum.getInstance(dispatchDetailDo.getStatus()) == UserDispatchDetailStatusEnum.FAIL) {
                            UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                            userInfo.setUserId(eventPushBatch.getUserId());
                            userInfo.setMobile(eventPushBatch.getMobile());
                            userInfo.setApp(eventPushBatchDo.getApp());
                            userInfoList.add(userInfo);
                            if (execLogDo != null) {
                                this.redisCount(execLogDo, date, RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_FAIL_COUNT);
                            }
                            // 失败计数器
                            log.info("电销回执结果失败, userid:{},strategyId:{},flowno:{}", eventPushBatchDo.getUserId(),
                                    eventPushBatch.getStrategyId(), req.getFlowNo());
                            Tracer.logMetricForCount("failed_voice_" + eventPushBatchDo.getStrategyId());
                            userSendCounterService.counterIncrementFailed(eventPushBatch.getUserId(),
                                    eventPushBatch.getStrategyId(), eventPushBatch.getMarketChannel(),
                                    DateUtil.dayOfInt(DateUtil.convert(eventPushBatch.getCreatedTime())));
                        } else {
                            if (execLogDo != null) {
                                this.redisCount(execLogDo, date, RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SUCCESS_COUNT);
                            }
                        }
                    }
                    UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
                    dispatchFailDetail.setStrategyId(eventPushBatchDo.getStrategyId());
                    dispatchFailDetail.setMarketChannel(eventPushBatchDo.getMarketChannel());
                    dispatchFailDetail.setTemplateNo(eventPushBatchDo.getTemplateId());
                    dispatchFailDetail.setList(userInfoList);
                    userDispatchFailDetailService.batchSave(dispatchFailDetail);
                });
            }
            log.info("电销回执接口响应正常, req:{}", JsonUtil.toJson(req));
            return Boolean.TRUE;
        } catch (Exception e) {
            log.warn("电销导入结果更新异常，批次号：{}", req.getFlowNo(), e);
            return Boolean.FALSE;
        }
    }
    /**
     * 更新用户下发结果
     *
     * @param req 推送记录
     * @return 更新结果
     */
    @Override
    public Boolean teleImportResult(TeleImportResultReq req) {
        return teleCall(req,StrategyMarketChannelEnum.VOICE);
    }

    private void redisCount(StrategyExecLogDo execLogDo, String date, String keyTemp) {
        String successCountKey = String.format(keyTemp, date, execLogDo.getStrategyId(), execLogDo.getStrategyMarketChannel(), execLogDo.getStrategyGroupId());
        if (redisUtils.hasKey(successCountKey)) {
            redisUtils.increment(successCountKey, 1L);
        }
    }

    /**
     * 统计策略成功/下发数
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     * @return 数量
     */
    @Override
    @Async("strategyCountTaskExecutor")
    public ImmutablePair<Integer, Integer> count(String tableNameNo, Long strategyId) {
        return ImmutablePair.of(successCount(tableNameNo, strategyId), totalCount(tableNameNo, strategyId));
    }

    /**
     * 统计策略成功数
     *
     * @param strategyId  策略ID
     * @param tableNameNo 表序号
     * @return 数量
     */
    @Override
    public Integer successCount(String tableNameNo, Long strategyId) {
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        int count = userDispatchDetailRepository.getCount(tableNameNo, strategyId, startTime, endTime, StrategyStatusEnum.SUCCESS.getCode());
        redisUtils.hPut(String.format(RedisKeyConstants.STRATEGY_DISPATCH_COUNT, strategyId), "succCount", String.valueOf(count), getSeconds());
        return count;
    }

    /**
     * 统计策略成功数
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     * @return 数量
     */
    @Override
    public Integer totalCount(String tableNameNo, Long strategyId) {
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        int count = userDispatchDetailRepository.getCount(tableNameNo, strategyId, startTime, endTime, null);
        redisUtils.hPut(String.format(RedisKeyConstants.STRATEGY_DISPATCH_COUNT, strategyId), "totalCount", String.valueOf(count), getSeconds());
        return count;
    }

    private Long getSeconds() {
        return Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.MAX)).getSeconds();
    }

    /**
     * 更新超过回执推送时间，用户状态为失败
     */
    @Override
    public void dispatchFailUserUpdate() {
        String tableNo = LocalDateTimeUtil.format(LocalDate.now().plusMonths(-1), "yyyyMM");
        List<UserDispatchDetailDo> userDispatchDetailDoList = null;
        while (true) {
            userDispatchDetailDoList = userDispatchDetailRepository.selectFailDispatchForUpdate(tableNo, config.getSmsReportOverTime());
            if (CollectionUtils.isEmpty(userDispatchDetailDoList)) {
                break;
            }
            userDispatchDetailRepository.dispatchFailUserUpdate(tableNo, userDispatchDetailDoList);
        }
        tableNo = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMM");
        while (true) {
            userDispatchDetailDoList = userDispatchDetailRepository.selectFailDispatchForUpdate(tableNo, config.getSmsReportOverTime());
            if (CollectionUtils.isEmpty(userDispatchDetailDoList)) {
                break;
            }
            userDispatchDetailRepository.dispatchFailUserUpdate(tableNo, userDispatchDetailDoList);
        }
    }

    @Override
    public DispatchRecordQueryResp hasDispatchRecord(DispatchRecordQueryReq args) {
        Integer days = args.getDays();
        if (Objects.isNull(days) || days <= 0 || days > strategyConfig.getHasDispatchRecordDays()) {
            throw new StrategyException("天数异常");
        }
        List<Triple<Integer, LocalDate, LocalDate>> tripleList = getTableNoAndTime(days);
        tripleList = tripleList.stream().sorted(Comparator.comparing(Triple::getLeft, Comparator.reverseOrder())).collect(Collectors.toList());
        for (Triple<Integer, LocalDate, LocalDate> triple : tripleList) {
            int count = userDispatchDetailRepository.getIndexFromLocal(
                    String.valueOf(triple.getLeft()),
                    args.getAppUserId(),
                    args.getStrategyId(),
                    args.getMarketChannel(),
                    LocalDateTime.of(triple.getMiddle(), LocalTime.of(0, 0, 0)),
                    LocalDateTime.of(triple.getRight(), LocalTime.of(23, 59, 59))
            );
            if (count > 0) {
                return new DispatchRecordQueryResp(1);
            }
        }
        return new DispatchRecordQueryResp(0);
    }

    @Override
    public boolean updateById(String tableNameNo, UserDispatchDetailDo userDispatchDetailDo) {
        return userDispatchDetailRepository.updateById(tableNameNo,userDispatchDetailDo);
    }

    @Override
    public int batchUpdateDispatchFail(String tableNo, List<Long> idList) {
        return userDispatchDetailRepository.batchUpdateDispatchFail(tableNo, idList);
    }

    @Override
    public boolean increaseAmtNotify(IncreaseAmtCallbakRequest request) {
        if (StringUtils.isEmpty(request.getUnique_flow_number())) {
            return false;
        }
        NotifyEventDto notifyEventDto = new NotifyEventDto();
        notifyEventDto.setEventTime(System.currentTimeMillis());
        notifyEventDto.setAppUserId(Long.parseLong(request.getExtend_data().get("xyf_cdp_user_id").toString()));
        notifyEventDto.setApp(request.getExtend_data().get("xyf_cdp_user_app").toString());
        notifyEventDto.setInnerApp(request.getExtend_data().get("xyf_cdp_user_innerApp").toString());
        notifyEventDto.setMobile(request.getExtend_data().get("xyf_cdp_user_mobile").toString());
        notifyEventDto.setExtraData(JsonUtil.toJson(request));
        // 发送回执消息
        mqTemplate.syncSend("tp_xyf_cdp_notify:tg_increaseamt", JsonUtil.toJson(notifyEventDto));
        log.info("提额回执，发送消息, topic:tp_xyf_cdp_notify:tg_increaseamt, 消息内容:{}", JsonUtil.toJson(notifyEventDto));

        String month = request.getExtend_data().get("xyf_cdp_dt").toString();
        Long id =  Long.valueOf(request.getExtend_data().get("xyf_cdp_pk").toString());
        String strategyType = request.getExtend_data().get("xyf_cdp_strategyType").toString();
        boolean succeed = StringUtils.equalsIgnoreCase("S", request.getAdjustment_result());

        UserDispatchDetailDo userDispatchDetailDo = userDispatchDetailRepository.selectById(month, id);
        if (userDispatchDetailDo == null || !Objects.equals(notifyEventDto.getAppUserId(), userDispatchDetailDo.getUserId())) {
            log.error("提额结果回执, 参数验证不合法, req:{}", JsonUtil.toJson(request));
            return false;
        }
        if (succeed) {
            int rt = 0;
            // 提额成功
            if (StringUtils.equalsIgnoreCase("offline", strategyType)) {
                // 离线
                rt = userDispatchDetailRepository.batchUpdateDispatchSucceedFromInit(month, Collections.singletonList(id));
            } else {
                // 实时
                rt = userDispatchDetailRepository.batchUpdateDispatchSucceedFromInit(month, Collections.singletonList(id));
            }
            log.info("提额结果成功, 更新成功数量:{},  userId:{}", rt, notifyEventDto.getAppUserId());
        } else {
            int rt = 0;
            if (StringUtils.equalsIgnoreCase("offline", strategyType)) {
                // 离线
                rt = userDispatchDetailRepository.batchUpdateDispatchFailFromInit(month, Collections.singletonList(id));
            } else {
                // 实时
                rt = userDispatchDetailRepository.batchUpdateDispatchFailFromInit(month, Collections.singletonList(id));
            }
            log.info("提额结果失败, 更新成功数量:{},  userId:{}", rt, notifyEventDto.getAppUserId());
        }
        return true;
    }

    @Override
    public ExistDispatchRecordResp existDispatchRecord(ExistDispatchRecordReq args) {
        if (Objects.equals(args.getAppUserId(), null)) {
            throw new BizException("appUserId cannot is null");
        }
        String strategyIds = CollectionUtils.isEmpty(args.getStrategyIdList()) ? "null" : args.getStrategyIdList().stream().sorted().map(String::valueOf).collect(Collectors.joining("_"));
        String redisKey = String.format(RedisKeyConstants.STRATEGY_DISPATCH_CURRENT_TIME, args.getAppUserId(), strategyIds);
        Long lastDispatchTime = redisUtils.getToLong(redisKey);
        long firstTime = LocalDateTime.now().plusHours(-24).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        if (lastDispatchTime != null && lastDispatchTime > firstTime) {
            return new ExistDispatchRecordResp(1);
        }
        List<LocalDateTime> queryDispatchTimeList = new ArrayList<>();
        LocalDateTime queryLastDispatchTime = null;
        List<Triple<String, LocalDateTime, LocalDateTime>> tripleList = getTableNoAndTimeCurrent24Hours();
        List<Future> tasks = new ArrayList<>();
        for (Triple<String, LocalDateTime, LocalDateTime> triple : tripleList) {
            Future<LocalDateTime> task = DispatchQueryDBExecutor.getPool().submit(() -> userDispatchDetailRepository.getMaxDispatchTime(triple.getLeft(), args.getAppUserId(), args.getStrategyIdList(), triple.getMiddle(), triple.getRight()));
            tasks.add(task);
        }

        tasks.forEach(t -> {
            try {
                LocalDateTime dispatchTime = (LocalDateTime) t.get();
                queryDispatchTimeList.add(dispatchTime);
            } catch (Exception e) {
                log.error("dispatchQueryDBExecutor result error", e);
            }
        });

        for (LocalDateTime dispatchTime : queryDispatchTimeList) {
            if (dispatchTime != null && (queryLastDispatchTime == null || dispatchTime.isAfter(queryLastDispatchTime))) {
                queryLastDispatchTime = dispatchTime;
            }
        }

        if (queryLastDispatchTime != null) {
            redisUtils.set(redisKey, queryLastDispatchTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), appConfigService.get24HourDispatchTimesRedisExpire());
            return new ExistDispatchRecordResp(1);
        }
        return new ExistDispatchRecordResp(0);
    }


    private static List<Triple<String, LocalDateTime, LocalDateTime>> getTableNoAndTimeCurrent24Hours() {
        List<Triple<String, LocalDateTime, LocalDateTime>> list = new ArrayList<>();
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.plusHours(-24);
        if (Objects.equals(LocalDateTimeUtil.format(startTime, "yyyyMM"), LocalDateTimeUtil.format(endTime, "yyyyMM"))) {
            list.add(Triple.of(LocalDateTimeUtil.format(startTime, "yyyyMM"), startTime, endTime));
        } else {
            LocalDateTime lastTimeOfMonth = LocalDateTimeUtil.endOfDay(startTime);
            list.add(Triple.of(LocalDateTimeUtil.format(startTime, "yyyyMM"), startTime, lastTimeOfMonth));
            list.add(Triple.of(LocalDateTimeUtil.format(endTime, "yyyyMM"), lastTimeOfMonth, endTime));
        }
        return list;
    }

    private static List<Triple<Integer, LocalDate, LocalDate>> getTableNoAndTime(Integer days) {
        List<Triple<Integer, LocalDate, LocalDate>> list = new ArrayList<>();
        LocalDate today = LocalDate.now();
        LocalDate startDate = today.minusDays(days).plusDays(1);
        while (days > 0) {
            String tableNo = localDateFormat(startDate);
            LocalDate endDate = StringUtils.equals(tableNo, localDateFormat(today)) ? today : startDate.with(TemporalAdjusters.lastDayOfMonth());
            list.add(Triple.of(Integer.valueOf(tableNo), startDate, endDate));

            int daysInMonth = endDate.getDayOfMonth() - startDate.getDayOfMonth() + 1;
            startDate = startDate.plusDays(daysInMonth);
            days -= daysInMonth;
        }
        return list;
    }

    private static String localDateFormat(LocalDate date) {
        return LocalDateTimeUtil.format(date, "yyyyMM");
    }
}