package com.xftech.cdp.domain.strategy.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Data
public class UserDispatchFailDetailDto {
    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 营销渠道，0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;

    /**
     * 模板编号
     */
    private String templateNo;

    /**
     * 用户集合
     */
    private List<UserInfo> list;

    /**
     * 识别原因
     */
    private String failReason;


    private String groupName;

    private String bizEventType;

    private String dispatchType;


    @Data
    public static class UserInfo {
        /**
         * credit_user.id
         */
        private Long userId;

        /**
         * APP
         */
        private String app;


        private String mobile;
    }
}
