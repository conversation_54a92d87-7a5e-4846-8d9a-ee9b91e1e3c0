package com.xinfei.touch.domain.service;

import com.xinfei.touch.domain.model.ReceiptMessage;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.repository.TouchRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 回执领域服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReceiptDomainService {
    
    private final TouchRecordRepository touchRecordRepository;
    
    /**
     * 处理回执消息
     * 
     * @param receiptMessage 回执消息
     */
    public void processReceipt(ReceiptMessage receiptMessage) {
        try {
            log.info("处理回执消息: requestId={}, batchNo={}, channel={}, status={}", 
                    receiptMessage.getRequestId(), receiptMessage.getBatchNo(), 
                    receiptMessage.getChannel(), receiptMessage.getStatus());
            
            // 1. 验证回执消息
            validateReceiptMessage(receiptMessage);
            
            // 2. 更新触达记录状态
            updateTouchRecordStatus(receiptMessage);
            
            log.info("回执消息处理完成: requestId={}, status={}", 
                    receiptMessage.getRequestId(), receiptMessage.getStatus());
            
        } catch (Exception e) {
            log.error("处理回执消息失败: requestId={}", receiptMessage.getRequestId(), e);
            throw new RuntimeException("回执处理失败", e);
        }
    }
    
    /**
     * 验证回执消息
     */
    private void validateReceiptMessage(ReceiptMessage receiptMessage) {
        if (receiptMessage == null) {
            throw new IllegalArgumentException("回执消息不能为空");
        }
        
        if (receiptMessage.getRequestId() == null || receiptMessage.getRequestId().trim().isEmpty()) {
            throw new IllegalArgumentException("请求ID不能为空");
        }
        
        if (receiptMessage.getBatchNo() == null || receiptMessage.getBatchNo().trim().isEmpty()) {
            throw new IllegalArgumentException("批次号不能为空");
        }
        
        if (receiptMessage.getChannel() == null) {
            throw new IllegalArgumentException("触达渠道不能为空");
        }
        
        if (receiptMessage.getStatus() == null) {
            throw new IllegalArgumentException("回执状态不能为空");
        }
    }
    
    /**
     * 更新触达记录状态
     */
    private void updateTouchRecordStatus(ReceiptMessage receiptMessage) {
        try {
            // 根据批次号更新触达记录状态
            int updateCount = touchRecordRepository.updateStatusByBatchNo(
                    receiptMessage.getBatchNo(),
                    receiptMessage.getStatus(),
                    receiptMessage.getErrorCode(),
                    receiptMessage.getErrorMessage(),
                    receiptMessage.getReceiptTime()
            );
            
            if (updateCount == 0) {
                log.warn("未找到对应的触达记录: batchNo={}", receiptMessage.getBatchNo());
            } else {
                log.debug("更新触达记录状态成功: batchNo={}, updateCount={}", 
                        receiptMessage.getBatchNo(), updateCount);
            }
            
        } catch (Exception e) {
            log.error("更新触达记录状态失败: batchNo={}", receiptMessage.getBatchNo(), e);
            throw e;
        }
    }
    

}
