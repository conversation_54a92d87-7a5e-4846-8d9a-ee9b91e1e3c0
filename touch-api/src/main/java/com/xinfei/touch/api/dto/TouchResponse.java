package com.xinfei.touch.api.dto;

import com.xinfei.touch.domain.model.TouchStatus;
import lombok.Data;

/**
 * 触达响应统一模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchResponse {
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 触达状态：SUCCESS, FAILED, FLOW_CONTROLLED, PENDING
     */
    private TouchStatus status;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 响应时间戳
     */
    private Long timestamp;
    
    /**
     * 扩展信息
     */
    private Object data;
    
    /**
     * 创建成功响应
     */
    public static TouchResponse success(String requestId, String batchNo) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.SUCCESS);
        response.setBatchNo(batchNo);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static TouchResponse failed(String requestId, String errorCode, String errorMessage) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.FAILED);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建流控响应
     */
    public static TouchResponse flowControlled(String requestId) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.FLOW_CONTROLLED);
        response.setErrorCode("FLOW_CONTROL");
        response.setErrorMessage("触达被流控拦截");
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 创建待处理响应
     */
    public static TouchResponse pending(String requestId) {
        TouchResponse response = new TouchResponse();
        response.setRequestId(requestId);
        response.setStatus(TouchStatus.PENDING);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
}
