package com.xinfei.touch.application.service;

import com.xinfei.touch.domain.model.TouchChannel;
import lombok.Data;

import java.util.Map;

/**
 * 渠道配置模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ChannelConfig {
    
    /**
     * 渠道类型
     */
    private TouchChannel channel;
    
    /**
     * 服务地址
     */
    private String serviceUrl;
    
    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 连接池大小
     */
    private Integer poolSize;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 权重（用于负载均衡）
     */
    private Integer weight;
    
    /**
     * 渠道特定参数
     */
    private Map<String, String> params;
    
    /**
     * 扩展配置
     */
    private Map<String, Object> extConfig;
    
    /**
     * 创建默认配置
     */
    public static ChannelConfig createDefault(TouchChannel channel) {
        ChannelConfig config = new ChannelConfig();
        config.setChannel(channel);
        config.setTimeout(30000);
        config.setRetryTimes(3);
        config.setPoolSize(10);
        config.setEnabled(true);
        config.setWeight(100);
        return config;
    }
    
    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled;
    }
    
    /**
     * 获取超时时间，如果未配置则返回默认值
     */
    public int getTimeoutOrDefault() {
        return timeout != null ? timeout : 30000;
    }
    
    /**
     * 获取重试次数，如果未配置则返回默认值
     */
    public int getRetryTimesOrDefault() {
        return retryTimes != null ? retryTimes : 3;
    }
    
    /**
     * 获取权重，如果未配置则返回默认值
     */
    public int getWeightOrDefault() {
        return weight != null ? weight : 100;
    }
}
