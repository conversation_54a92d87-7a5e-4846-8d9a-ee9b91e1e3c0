package com.xftech.cdp;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@EnableApolloConfig
@RestController
@EnableScheduling
@EnableAsync
@EnableFeignClients
@SpringBootApplication(scanBasePackages = {"com.xftech", "com.xyf", "cn.xinfei"})
@EnableConfigurationProperties
@ImportResource("classpath:springbean/chgctrl-client.xml")
public class CdpApplication {
    public static void main(String[] args) {
        SpringApplication.run(CdpApplication.class);
    }

    @GetMapping("/hc")
    public String healthCheck() {
        return "OK";
    }
}